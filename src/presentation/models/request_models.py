"""Request models for the API."""
from pydantic import BaseModel, Field, field_validator
from enum import Enum
from typing import Optional


class MediaType(str, Enum):
    """Media type enumeration."""
    VIDEO = "video"
    AUDIO = "audio"
    AUTO = "auto"  # Let the system decide based on URL


class DownloadRequest(BaseModel):
    """Request model for downloading media."""
    chat_id: int = Field(..., description="Telegram chat ID")
    url: str = Field(..., description="URL to download media from")
    bot_token: str = Field(..., description="Telegram bot token")
    media_type: MediaType = Field(default=MediaType.AUTO, description="Type of media to download (video/audio/auto)")
    video_format: Optional[str] = Field(default=None, description="Video format for YouTube downloads (144p, 240p, 360p, 480p, 720p, 1080p, 1440p, 2160p, mp3)")

    @field_validator('chat_id')
    @classmethod
    def validate_chat_id(cls, v):
        if not isinstance(v, int):
            raise ValueError('Chat ID must be an integer')
        return v

    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('URL cannot be empty')

        # Basic URL validation
        if not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('URL must start with http:// or https://')

        return v

    @field_validator('bot_token')
    @classmethod
    def validate_bot_token(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Bot token cannot be empty')

        if len(v) < 10:
            raise ValueError('Bot token appears to be invalid')

        return v

    @field_validator('video_format')
    @classmethod
    def validate_video_format(cls, v):
        if v is None:
            return v

        valid_formats = ["144p", "240p", "360p", "480p", "720p", "1080p", "1440p", "2160p", "mp3"]
        if v not in valid_formats:
            raise ValueError(f'Video format must be one of: {", ".join(valid_formats)}')

        return v


class SearchMusicRequest(BaseModel):
    """Request model for searching music."""
    query: str = Field(..., description="Search query for music")
    page: int = Field(default=1, description="Page number for pagination", ge=1)

    @field_validator('query')
    @classmethod
    def validate_query(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Query cannot be empty')

        if len(v.strip()) < 2:
            raise ValueError('Query must be at least 2 characters long')

        return v.strip()


class DownloadFromShortcodeRequest(BaseModel):
    """Request model for downloading from YouTube shortcode."""
    chat_id: int = Field(..., description="Telegram chat ID")
    shortcode: str = Field(..., description="YouTube video shortcode")
    bot_token: str = Field(..., description="Telegram bot token")
    media_type: MediaType = Field(default=MediaType.AUDIO, description="Type of media to download (video/audio)")

    @field_validator('chat_id')
    @classmethod
    def validate_chat_id(cls, v):
        if not isinstance(v, int):
            raise ValueError('Chat ID must be an integer')
        return v

    @field_validator('shortcode')
    @classmethod
    def validate_shortcode(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Shortcode cannot be empty')

        if len(v.strip()) < 5:
            raise ValueError('Shortcode appears to be invalid')

        return v.strip()

    @field_validator('bot_token')
    @classmethod
    def validate_bot_token(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('Bot token cannot be empty')

        if len(v) < 10:
            raise ValueError('Bot token appears to be invalid')

        return v

"""Response models for the API."""
from pydantic import BaseModel, Field
from typing import Optional, List


class DownloadResponse(BaseModel):
    """Response model for download operations."""
    status: str = Field(..., description="Status of the operation (success/error)")
    message: str = Field(..., description="Human-readable message")
    file_id: Optional[str] = Field(None, description="Telegram file ID if successful")


class ErrorResponse(BaseModel):
    """Response model for errors."""
    status: str = Field(default="error", description="Status of the operation")
    message: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str = Field(default="healthy", description="Health status")
    version: str = Field(default="1.0.0", description="API version")
    timestamp: str = Field(..., description="Current timestamp")


class MusicSearchResult(BaseModel):
    """Individual music search result."""
    title: str = Field(..., description="Music title")
    shortcode: str = Field(..., description="YouTube shortcode")
    duration: str = Field(..., description="Duration in format MM:SS")
    thumb: str = Field(..., description="Thumbnail URL")
    thumb_best: str = Field(..., description="Best quality thumbnail URL")


class SearchMusicResponse(BaseModel):
    """Response model for music search."""
    error: bool = Field(..., description="Whether there was an error")
    page: int = Field(..., description="Current page number")
    results: List[MusicSearchResult] = Field(..., description="List of search results")

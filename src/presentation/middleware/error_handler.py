"""Global error handler middleware for FastAPI."""
import logging
from typing import Union
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.presentation.models.response_models import ErrorResponse
from src.presentation.dependencies import get_error_notification_service


logger = logging.getLogger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware to handle all unhandled exceptions."""

    async def dispatch(self, request: Request, call_next):
        """Process request and handle any exceptions."""
        try:
            response = await call_next(request)
            return response
        except HTTPException:
            # Let FastAPI handle HTTP exceptions normally
            raise
        except Exception as e:
            # Handle unexpected exceptions
            logger.error(f"Unhandled exception in {request.url.path}: {e}", exc_info=True)
            
            # Try to get bot token from request for error notification
            bot_token = None
            try:
                if request.method == "POST":
                    body = await request.body()
                    # This is a simplified approach - in real implementation you might need
                    # to parse JSON body to extract bot_token
                    pass
            except Exception:
                pass

            # Send error notification to admin if bot_token is available
            if bot_token:
                try:
                    error_service = get_error_notification_service(bot_token)
                    error_type = error_service.classify_error(e)
                    await error_service.notify_admin_error(
                        error_message=str(e),
                        error_type=error_type,
                        context={
                            "path": str(request.url.path),
                            "method": request.method,
                            "user_agent": request.headers.get("user-agent", "Unknown")
                        }
                    )
                except Exception as notification_error:
                    logger.error(f"Failed to send error notification: {notification_error}")

            # Return user-friendly error response
            return JSONResponse(
                status_code=500,
                content=ErrorResponse(
                    status="error",
                    message="Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                ).model_dump()
            )


async def handle_validation_error(request: Request, exc: Exception) -> JSONResponse:
    """Handle validation errors."""
    logger.warning(f"Validation error in {request.url.path}: {exc}")
    
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            status="error",
            message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting.",
            detail=str(exc)
        ).model_dump()
    )


async def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions."""
    logger.warning(f"HTTP exception in {request.url.path}: {exc.status_code} - {exc.detail}")
    
    # Map HTTP status codes to user-friendly messages
    user_messages = {
        400: "Noto'g'ri so'rov. Iltimos, ma'lumotlarni tekshiring.",
        401: "Ruxsat berilmagan. Iltimos, qayta kirish qiling.",
        403: "Ruxsat yo'q. Ushbu amalni bajarish uchun huquqingiz yo'q.",
        404: "Sahifa topilmadi. Iltimos, to'g'ri manzilni kiriting.",
        429: "Juda ko'p so'rov. Iltimos, biroz kuting.",
        500: "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
        502: "Server javob bermayapti. Iltimos, keyinroq qayta urinib ko'ring.",
        503: "Servis vaqtincha ishlamayapti. Iltimos, keyinroq qayta urinib ko'ring."
    }
    
    user_message = user_messages.get(exc.status_code, "Kutilmagan xatolik yuz berdi.")
    
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            status="error",
            message=user_message,
            detail=str(exc.detail) if exc.status_code < 500 else None
        ).model_dump()
    )

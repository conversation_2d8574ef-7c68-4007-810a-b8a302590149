"""Dependency injection for the presentation layer."""
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.infrastructure.config.container import container


def get_download_media_use_case(bot_token: str):
    """Get download media use case instance."""
    return container.get_download_media_use_case(bot_token)


def get_search_music_use_case():
    """Get search music use case instance."""
    return container.get_search_music_use_case()


def get_download_from_shortcode_use_case(bot_token: str):
    """Get download from shortcode use case instance."""
    return container.get_download_from_shortcode_use_case(bot_token)


def get_music_cache_repository():
    """Get music cache repository instance."""
    return container.get_music_cache_repository()


def get_error_notification_service(bot_token: str):
    """Get error notification service instance."""
    return container.get_error_notification_service(bot_token)

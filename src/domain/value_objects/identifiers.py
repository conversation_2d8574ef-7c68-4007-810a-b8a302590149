"""Identifier value objects."""
from dataclasses import dataclass


@dataclass(frozen=True)
class ChatId:
    """Value object for Telegram chat ID."""
    value: int

    def __post_init__(self):
        if not isinstance(self.value, int):
            raise ValueError("Chat ID must be an integer")


@dataclass(frozen=True)
class BotToken:
    """Value object for Telegram bot token."""
    value: str

    def __post_init__(self):
        if not self.value or not isinstance(self.value, str):
            raise ValueError("Bot token cannot be empty")
        
        if len(self.value) < 10:  # Basic validation
            raise ValueError("Bot token appears to be invalid")


@dataclass(frozen=True)
class UserId:
    """Value object for user ID."""
    value: int

    def __post_init__(self):
        if not isinstance(self.value, int):
            raise ValueError("User ID must be an integer")

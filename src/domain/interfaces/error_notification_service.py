"""Interface for error notification service."""
from abc import ABC, abstractmethod
from typing import Optional


class IErrorNotificationService(ABC):
    """Interface for error notification service."""

    @abstractmethod
    async def notify_admin_error(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict] = None,
        user_chat_id: Optional[str] = None
    ) -> None:
        """
        Notify admin about an error.
        
        Args:
            error_message: The error message
            error_type: Type of error (e.g., 'API_ERROR', 'VALIDATION_ERROR', etc.)
            context: Additional context information
            user_chat_id: Chat ID where the error occurred (if applicable)
        """
        pass

    @abstractmethod
    def get_user_friendly_message(self, error_type: str, original_error: str) -> str:
        """
        Get user-friendly error message in Uzbek.
        
        Args:
            error_type: Type of error
            original_error: Original error message
            
        Returns:
            User-friendly error message in Uzbek
        """
        pass

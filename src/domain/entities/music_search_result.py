"""Music search result entity."""
from dataclasses import dataclass


@dataclass
class MusicSearchResult:
    """Represents a music search result."""
    title: str
    shortcode: str
    duration: str
    thumb: str
    thumb_best: str
    
    def __post_init__(self):
        """Validate the music search result."""
        if not self.title:
            raise ValueError("Title cannot be empty")
        if not self.shortcode:
            raise ValueError("Shortcode cannot be empty")
        if not self.duration:
            raise ValueError("Duration cannot be empty")
        if not self.thumb:
            raise ValueError("Thumb URL cannot be empty")
        if not self.thumb_best:
            raise ValueError("Thumb best URL cannot be empty")
    
    @property
    def youtube_url(self) -> str:
        """Get the full YouTube URL from shortcode."""
        return f"https://www.youtube.com/watch?v={self.shortcode}"


@dataclass
class MusicSearchResponse:
    """Represents a music search response."""
    error: bool
    page: int
    results: list[MusicSearchResult]
    
    def __post_init__(self):
        """Validate the music search response."""
        if self.page < 1:
            raise ValueError("Page must be greater than 0")
        if not isinstance(self.results, list):
            raise ValueError("Results must be a list")

"""SQLite implementation of music cache repository."""
import sqlite3
import logging
import json
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from src.domain.interfaces.repositories import IMusicCacheRepository
from src.domain.entities.music_search_result import MusicSearchResult
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class SQLiteMusicCacheRepository(IMusicCacheRepository):
    """SQLite implementation of music cache repository."""

    def __init__(self, db_path: str = None):
        self.db_path = db_path or settings.database.path
        self._init_db()

    def _init_db(self):
        """Initialize the database."""
        conn = sqlite3.connect(self.db_path)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS music_cache 
                     (shortcode TEXT PRIMARY KEY, 
                      title TEXT, 
                      duration TEXT, 
                      thumb TEXT, 
                      thumb_best TEXT, 
                      cached_at TEXT)''')
        conn.commit()
        conn.close()

    def _get_connection(self) -> sqlite3.Connection:
        """Get database connection."""
        return sqlite3.connect(self.db_path)

    async def save_search_result(self, shortcode: str, result: MusicSearchResult) -> None:
        """Save search result to cache."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            cached_at = datetime.now().isoformat()
            
            # Use INSERT OR REPLACE to update existing entries
            c.execute(
                """INSERT OR REPLACE INTO music_cache 
                   (shortcode, title, duration, thumb, thumb_best, cached_at) 
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (shortcode, result.title, result.duration, result.thumb, result.thumb_best, cached_at)
            )
            conn.commit()
            conn.close()
            
            logger.info(f"Cached search result for shortcode: {shortcode}")
            
        except Exception as e:
            logger.error(f"Error saving search result to cache: {e}")
            raise

    async def get_cached_result(self, shortcode: str) -> Optional[MusicSearchResult]:
        """Get cached search result by shortcode."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            c.execute(
                "SELECT title, duration, thumb, thumb_best, cached_at FROM music_cache WHERE shortcode = ?",
                (shortcode,)
            )
            
            row = c.fetchone()
            conn.close()
            
            if row:
                title, duration, thumb, thumb_best, cached_at = row
                
                # Check if cache is still valid (24 hours)
                cached_time = datetime.fromisoformat(cached_at)
                if datetime.now() - cached_time <= timedelta(hours=24):
                    logger.info(f"Found valid cached result for shortcode: {shortcode}")
                    return MusicSearchResult(
                        title=title,
                        shortcode=shortcode,
                        duration=duration,
                        thumb=thumb,
                        thumb_best=thumb_best
                    )
                else:
                    logger.info(f"Cached result expired for shortcode: {shortcode}")
                    # Remove expired entry
                    await self._remove_expired_entry(shortcode)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting cached result: {e}")
            return None

    async def is_cache_valid(self, shortcode: str, max_age_hours: int = 24) -> bool:
        """Check if cached result is still valid."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            c.execute(
                "SELECT cached_at FROM music_cache WHERE shortcode = ?",
                (shortcode,)
            )
            
            row = c.fetchone()
            conn.close()
            
            if row:
                cached_at = datetime.fromisoformat(row[0])
                return datetime.now() - cached_at <= timedelta(hours=max_age_hours)
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking cache validity: {e}")
            return False

    async def cleanup_expired_cache(self, max_age_hours: int = 24) -> None:
        """Remove expired cache entries."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            cutoff_time = (datetime.now() - timedelta(hours=max_age_hours)).isoformat()
            
            c.execute(
                "DELETE FROM music_cache WHERE cached_at < ?",
                (cutoff_time,)
            )
            
            deleted_count = c.rowcount
            conn.commit()
            conn.close()
            
            if deleted_count > 0:
                logger.info(f"Cleaned up {deleted_count} expired cache entries")
            
        except Exception as e:
            logger.error(f"Error cleaning up expired cache: {e}")

    async def _remove_expired_entry(self, shortcode: str) -> None:
        """Remove a specific expired entry."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            c.execute("DELETE FROM music_cache WHERE shortcode = ?", (shortcode,))
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error removing expired entry: {e}")

    async def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        try:
            conn = self._get_connection()
            c = conn.cursor()
            
            # Total entries
            c.execute("SELECT COUNT(*) FROM music_cache")
            total_entries = c.fetchone()[0]
            
            # Valid entries (within 24 hours)
            cutoff_time = (datetime.now() - timedelta(hours=24)).isoformat()
            c.execute("SELECT COUNT(*) FROM music_cache WHERE cached_at >= ?", (cutoff_time,))
            valid_entries = c.fetchone()[0]
            
            conn.close()
            
            return {
                "total_entries": total_entries,
                "valid_entries": valid_entries,
                "expired_entries": total_entries - valid_entries
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"total_entries": 0, "valid_entries": 0, "expired_entries": 0}

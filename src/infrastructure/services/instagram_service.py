"""Instagram service implementation."""
import httpx
import logging
from urllib.parse import quote

from src.domain.interfaces.services import IInstagramService
from src.domain.entities.media_item import MediaItem, MediaType, Album, AlbumItem
from src.domain.value_objects.url import Url
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class InstagramService(IInstagramService):
    """Instagram service implementation."""

    def __init__(self):
        self.api_base_url = settings.instagram.api_base_url
        self.api_token = settings.instagram.api_token

        if not self.api_token:
            logger.warning("Instagram API token not configured. Set INSTAGRAM_API_TOKEN environment variable.")

    async def get_media_info(self, url: Url) -> MediaItem:
        """Get media information from Instagram URL."""
        if not self.api_token:
            raise Exception("Instagram API token not configured. Please set INSTAGRAM_API_TOKEN environment variable.")

        try:
            encoded_url = quote(url.value, safe='')
            api_url = f"{self.api_base_url}?url={encoded_url}&token={self.api_token}"

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(api_url)
                response.raise_for_status()

                data = response.json()
                if data.get("error"):
                    raise Exception(f"API Error: {data.get('message', 'Unknown error')}")

                # Map API response to domain entity
                media_type_str = data.get("type", "").lower()
                if media_type_str == "album":
                    media_type = MediaType.ALBUM
                elif media_type_str == "video":
                    media_type = MediaType.VIDEO
                else:
                    media_type = MediaType.IMAGE

                return MediaItem(
                    url=url.value,
                    media_type=media_type,
                    download_url=data.get("download_url"),
                    caption=data.get("caption"),
                    duration=int(data.get("duration", 0)) if data.get("duration") else None,
                    width=data.get("width"),
                    height=data.get("height"),
                    thumbnail_url=data.get("thumbnail_url")
                )

        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise Exception(f"Failed to connect to Instagram API: {e}")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            raise Exception(f"Instagram API returned error: {e}")
        except Exception as e:
            logger.error(f"Error getting Instagram media info: {e}")
            raise

    async def get_album_info(self, url: Url) -> Album:
        """Get album information from Instagram URL."""
        if not self.api_token:
            raise Exception("Instagram API token not configured. Please set INSTAGRAM_API_TOKEN environment variable.")
            
        try:
            encoded_url = quote(url.value, safe='')
            api_url = f"{self.api_base_url}?url={encoded_url}&token={self.api_token}"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(api_url)
                response.raise_for_status()
                
                data = response.json()
                if data.get("error"):
                    raise Exception(f"API Error: {data.get('message', 'Unknown error')}")
                
                medias = data.get("medias", [])
                if not medias:
                    raise Exception("No media items found in album")

                album_items = []
                for i, media_item in enumerate(medias, 1):
                    item_type_str = media_item.get("type", "").lower()
                    item_type = MediaType.VIDEO if item_type_str == "video" else MediaType.IMAGE
                    
                    album_item = AlbumItem(
                        media_type=item_type,
                        download_url=media_item.get("download_url"),
                        index=i,
                        total_items=len(medias)
                    )
                    album_items.append(album_item)

                return Album(
                    items=album_items,
                    caption=data.get("caption")
                )
                
        except Exception as e:
            logger.error(f"Error getting Instagram album info: {e}")
            raise

    async def download_media(self, media_item) -> bytes:
        """Download media content."""
        try:
            download_url = None

            if hasattr(media_item, 'download_url'):
                download_url = media_item.download_url
            elif isinstance(media_item, dict):
                download_url = media_item.get('download_url')
            else:
                raise ValueError("Invalid media item format")

            if not download_url:
                raise Exception("No download URL available")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(download_url)
                response.raise_for_status()

                logger.info(f"Downloaded {len(response.content)} bytes from Instagram")
                return response.content

        except httpx.RequestError as e:
            logger.error(f"Request error downloading media: {e}")
            raise Exception(f"Failed to download media: {e}")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading media: {e}")
            raise Exception(f"Media download failed: {e}")
        except Exception as e:
            logger.error(f"Error downloading Instagram media: {e}")
            raise

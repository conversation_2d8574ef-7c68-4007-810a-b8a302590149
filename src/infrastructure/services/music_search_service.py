"""Music search service implementation."""
import httpx
import logging

from src.domain.interfaces.services import IMusicSearchService
from src.domain.interfaces.repositories import IMusicCacheRepository
from src.domain.entities.music_search_result import MusicSearchResult, MusicSearchResponse
from src.domain.entities.download_result import DownloadResult

logger = logging.getLogger(__name__)


class MusicSearchService(IMusicSearchService):
    """Music search service implementation using FastSaver API."""

    def __init__(self, cache_repository: IMusicCacheRepository = None):
        self.api_base_url = "https://fastsaverapi.com/search-music"
        self.api_token = "lxcMy0OtNaimyGEQkdHjXAmC"
        self.cache_repository = cache_repository



    async def search_music(self, query: str, page: int = 1) -> MusicSearchResponse:
        """Search for music using the FastSaver API."""
        try:
            logger.info(f"Searching music with query: {query}, page: {page}")

            # Prepare API request
            params = {
                "query": query,
                "page": page,
                "token": self.api_token
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(self.api_base_url, params=params)
                response.raise_for_status()

                data = response.json()
                
                # Check for API errors
                if data.get("error", False):
                    logger.error(f"API returned error for query: {query}")
                    return MusicSearchResponse(
                        error=True,
                        page=page,
                        results=[]
                    )

                # Convert API response to domain entities and cache them
                results = []
                for item in data.get("results", []):
                    try:
                        result = MusicSearchResult(
                            title=item.get("title", ""),
                            shortcode=item.get("shortcode", ""),
                            duration=item.get("duration", ""),
                            thumb=item.get("thumb", ""),
                            thumb_best=item.get("thumb_best", "")
                        )
                        results.append(result)

                        # Cache the result if cache repository is available
                        if self.cache_repository:
                            try:
                                await self.cache_repository.save_search_result(result.shortcode, result)
                            except Exception as e:
                                logger.warning(f"Failed to cache result for {result.shortcode}: {e}")

                    except ValueError as e:
                        logger.warning(f"Skipping invalid search result: {e}")
                        continue

                return MusicSearchResponse(
                    error=False,
                    page=data.get("page", page),
                    results=results
                )

        except httpx.RequestError as e:
            logger.error(f"Request error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error during music search: {e}")
            return MusicSearchResponse(
                error=True,
                page=page,
                results=[]
            )

    async def download_from_shortcode(self, shortcode: str, media_type: str = "audio", bot_username: str = "instasaver_bot") -> DownloadResult:
        """Download media from YouTube using FastSaver API."""
        try:
            # First check cache for title and duration
            cached_result = None
            if self.cache_repository:
                try:
                    cached_result = await self.cache_repository.get_cached_result(shortcode)
                    if cached_result:
                        logger.info(f"Using cached metadata for shortcode: {shortcode}")
                except Exception as e:
                    logger.warning(f"Failed to get cached result: {e}")

            # Use FastSaver API to download
            download_url = "https://fastsaverapi.com/download"

            # Determine format based on media_type
            format_param = "mp3" if media_type == "audio" else "mp4"

            params = {
                "video_id": shortcode,
                "format": format_param,
                "bot_username": bot_username,
                "token": self.api_token
            }

            logger.info(f"Downloading from FastSaver API: {shortcode}, format: {format_param}")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(download_url, params=params)
                response.raise_for_status()

                data = response.json()

                # Check for API errors
                if data.get("error", False):
                    logger.error(f"FastSaver API returned error for shortcode: {shortcode}")
                    return DownloadResult(
                        success=False,
                        message=f"Download failed: {data.get('message', 'Unknown error')}",
                        title=None
                    )

                # Extract file_id from response
                file_id = data.get("file_id")
                if not file_id:
                    return DownloadResult(
                        success=False,
                        message="No file_id returned from FastSaver API",
                        title=None
                    )

                # Get title from cached result or use shortcode as fallback
                title = None
                if cached_result:
                    title = cached_result.title

                # If no title from cache, use shortcode as fallback
                if not title:
                    title = f"YouTube Media {shortcode}"

                logger.info(f"FastSaver download successful: file_id={file_id}, title: {title}")

                return DownloadResult(
                    success=True,
                    message="Media downloaded successfully via FastSaver API",
                    file_path=None,  # No local file path since we use file_id
                    title=title,
                    telegram_file_id=file_id
                )

        except httpx.RequestError as e:
            logger.error(f"Request error during FastSaver download: {e}")
            return DownloadResult(
                success=False,
                message=f"Network error: {str(e)}",
                title=None
            )
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error during FastSaver download: {e}")
            return DownloadResult(
                success=False,
                message=f"API error: {str(e)}",
                title=None
            )
        except Exception as e:
            logger.error(f"Unexpected error during FastSaver download: {e}")
            return DownloadResult(
                success=False,
                message=f"Error: {str(e)}",
                title=None
            )

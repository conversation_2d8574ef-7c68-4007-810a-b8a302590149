"""Telegram service implementation."""
import os
import logging
from typing import Optional
from telegram import <PERSON><PERSON>
import telebot

from src.domain.interfaces.services import ITelegramService
from src.domain.value_objects.identifiers import Chat<PERSON><PERSON>, BotToken
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class TelegramService(ITelegramService):
    """Telegram service implementation."""

    def __init__(self, bot_token: BotToken):
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token.value)
        self.telebot_instance = telebot.TeleBot(token=bot_token.value)
        self._bot_username = None
        
        # Configure telebot for large files
        self._configure_telebot()

    def _configure_telebot(self):
        """Configure telebot for large file uploads."""
        telebot.apihelper.API_URL = settings.telegram.local_api_url
        telebot.apihelper.READ_TIMEOUT = settings.telegram.read_timeout
        telebot.apihelper.CONNECT_TIMEOUT = settings.telegram.connection_timeout

    async def _get_bot_username(self) -> str:
        """Get bot username from Telegram API."""
        if self._bot_username is None:
            try:
                bot_info = await self.bot.get_me()
                self._bot_username = bot_info.username
                logger.info(f"Bot username retrieved: {self._bot_username}")
            except Exception as e:
                logger.error(f"Error getting bot username: {e}")
                self._bot_username = "YuklaydiBot"  # Fallback username
        return self._bot_username

    async def get_bot_username_public(self) -> str:
        """Public method to get bot username."""
        return await self._get_bot_username()

    def _add_bot_username_to_caption(self, caption: Optional[str]) -> str:
        """Add bot username to caption."""
        bot_username = self._bot_username or "YuklaydiBot"
        bot_mention = f"👉 @{bot_username} orqali yuklandi"
        
        if caption:
            return f"{caption}\n\n{bot_mention}"
        else:
            return bot_mention

    async def send_photo(
        self, 
        chat_id: ChatId, 
        photo_path: str, 
        caption: Optional[str] = None
    ) -> str:
        """Send photo to Telegram chat. Returns file_id."""
        try:
            if not os.path.exists(photo_path):
                raise FileNotFoundError(f"Photo file not found: {photo_path}")

            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            with open(photo_path, "rb") as photo_file:
                msg = await self.bot.send_photo(
                    chat_id=chat_id.value,
                    photo=photo_file,
                    caption=enhanced_caption
                )
                
            if msg and msg.photo:
                file_id = msg.photo[-1].file_id
                logger.info(f"Photo sent successfully, file_id: {file_id}")
                return file_id
            else:
                raise Exception("Failed to send photo - no file_id returned")
                
        except Exception as e:
            logger.error(f"Error sending photo: {e}")
            raise

    async def send_video(
        self, 
        chat_id: ChatId, 
        video_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None
    ) -> str:
        """Send video to Telegram chat. Returns file_id."""
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            file_size = os.path.getsize(video_path) / (1024 * 1024)  # Size in MB

            if file_size > 2048:  # 2GB in MB
                raise ValueError("Video file size exceeds 2GB limit")

            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            # For large files, use telebot with local API
            if file_size > 50:  # Use telebot for files larger than 50MB
                return await self._send_large_video(
                    chat_id, video_path, enhanced_caption, duration, width, height, thumbnail_path
                )
            else:
                # Use python-telegram-bot for smaller files
                with open(video_path, "rb") as video_file:
                    msg = await self.bot.send_video(
                        chat_id=chat_id.value,
                        video=video_file,
                        caption=enhanced_caption,
                        duration=duration,
                        width=width,
                        height=height
                    )
                    
                if msg and msg.video:
                    file_id = msg.video.file_id
                    logger.info(f"Video sent successfully, file_id: {file_id}")
                    return file_id
                else:
                    raise Exception("Failed to send video - no file_id returned")
                    
        except Exception as e:
            logger.error(f"Error sending video: {e}")
            raise

    async def send_audio(
        self, 
        chat_id: ChatId, 
        audio_path: str, 
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        title: Optional[str] = None,
        performer: Optional[str] = None
    ) -> str:
        """Send audio to Telegram chat. Returns file_id."""
        try:
            if not os.path.exists(audio_path):
                raise FileNotFoundError(f"Audio file not found: {audio_path}")

            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            with open(audio_path, "rb") as audio_file:
                msg = await self.bot.send_audio(
                    chat_id=chat_id.value,
                    audio=audio_file,
                    caption=enhanced_caption,
                    duration=duration,
                    title=title,
                    performer=performer
                )
                
            if msg and msg.audio:
                file_id = msg.audio.file_id
                logger.info(f"Audio sent successfully, file_id: {file_id}")
                return file_id
            else:
                raise Exception("Failed to send audio - no file_id returned")
                
        except Exception as e:
            logger.error(f"Error sending audio: {e}")
            raise

    async def _send_large_video(
        self,
        chat_id: ChatId,
        video_path: str,
        caption: Optional[str] = None,
        duration: Optional[int] = None,
        width: Optional[int] = None,
        height: Optional[int] = None,
        thumbnail_path: Optional[str] = None
    ) -> str:
        """Send large video using telebot with local API."""
        try:
            with open(video_path, 'rb') as video:
                thumbnail = None
                if thumbnail_path and os.path.exists(thumbnail_path):
                    thumbnail = open(thumbnail_path, 'rb')

                try:
                    msg = self.telebot_instance.send_video(
                        chat_id=chat_id.value,
                        video=video,
                        caption=caption,
                        duration=duration,
                        width=width,
                        height=height,
                        thumbnail=thumbnail,
                        supports_streaming=True
                    )
                    
                    if msg and msg.video:
                        file_id = msg.video.file_id
                        logger.info(f"Large video sent successfully, file_id: {file_id}")
                        return file_id
                    else:
                        raise Exception("Failed to send large video - no file_id returned")
                        
                finally:
                    if thumbnail:
                        thumbnail.close()
                        
        except Exception as e:
            logger.error(f"Error sending large video: {e}")
            raise

    async def send_message(self, chat_id: ChatId, text: str) -> None:
        """Send text message to Telegram chat."""
        try:
            await self.bot.send_message(chat_id=chat_id.value, text=text)
            logger.info(f"Message sent to chat {chat_id.value}")

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise

    async def send_audio_by_file_id(
        self,
        chat_id: ChatId,
        file_id: str,
        caption: Optional[str] = None,
        title: Optional[str] = None,
        performer: Optional[str] = None
    ) -> str:
        """Send audio to Telegram chat using file_id. Returns file_id."""
        try:
            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            msg = await self.bot.send_audio(
                chat_id=chat_id.value,
                audio=file_id,
                caption=enhanced_caption,
                title=title,
                performer=performer
            )

            if msg and msg.audio:
                new_file_id = msg.audio.file_id
                logger.info(f"Audio sent by file_id successfully, new file_id: {new_file_id}")
                return new_file_id
            else:
                raise Exception("Failed to send audio by file_id - no file_id returned")

        except Exception as e:
            logger.error(f"Error sending audio by file_id: {e}")
            raise

    async def send_video_by_file_id(
        self,
        chat_id: ChatId,
        file_id: str,
        caption: Optional[str] = None
    ) -> str:
        """Send video to Telegram chat using file_id. Returns file_id."""
        try:
            # Get bot username and add to caption
            await self._get_bot_username()
            enhanced_caption = self._add_bot_username_to_caption(caption)

            msg = await self.bot.send_video(
                chat_id=chat_id.value,
                video=file_id,
                caption=enhanced_caption
            )

            if msg and msg.video:
                new_file_id = msg.video.file_id
                logger.info(f"Video sent by file_id successfully, new file_id: {new_file_id}")
                return new_file_id
            else:
                raise Exception("Failed to send video by file_id - no file_id returned")

        except Exception as e:
            logger.error(f"Error sending video by file_id: {e}")
            raise

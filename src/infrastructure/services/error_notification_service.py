"""Error notification service implementation."""
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from src.domain.interfaces.error_notification_service import IErrorNotificationService
from src.infrastructure.config.settings import settings


logger = logging.getLogger(__name__)


class ErrorNotificationService(IErrorNotificationService):
    """Error notification service implementation."""

    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token)
        self.admin_chat_id = settings.admin.chat_id
        self.notifications_enabled = settings.admin.enable_error_notifications

    async def notify_admin_error(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict] = None,
        user_chat_id: Optional[str] = None
    ) -> None:
        """Notify admin about an error."""
        if not self.notifications_enabled or not self.admin_chat_id:
            logger.warning("Admin error notifications are disabled or admin chat ID not configured")
            return

        try:
            # Format error message for admin
            admin_message = self._format_admin_error_message(
                error_message, error_type, context, user_chat_id
            )

            # Send message to admin
            await self.bot.send_message(
                chat_id=self.admin_chat_id,
                text=admin_message,
                parse_mode='HTML'
            )

            logger.info(f"Error notification sent to admin: {error_type}")

        except TelegramError as e:
            logger.error(f"Failed to send error notification to admin: {e}")
        except Exception as e:
            logger.error(f"Unexpected error while sending admin notification: {e}")

    def get_user_friendly_message(self, error_type: str, original_error: str) -> str:
        """Get user-friendly error message in Uzbek."""
        error_messages = {
            'INSTAGRAM_API_ERROR': "Instagram'dan ma'lumot olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'YOUTUBE_API_ERROR': "YouTube'dan ma'lumot olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'MUSIC_SEARCH_ERROR': "Musiqa qidiruvida xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'DOWNLOAD_ERROR': "Fayl yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'TELEGRAM_ERROR': "Telegram'ga fayl yuborishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'VALIDATION_ERROR': "Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting.",
            'FILE_NOT_FOUND': "Fayl topilmadi. Iltimos, boshqa havolani sinab ko'ring.",
            'FILE_SIZE_ERROR': "Fayl hajmi juda katta. Maksimal hajm 2GB.",
            'NETWORK_ERROR': "Internet aloqasida muammo. Iltimos, keyinroq qayta urinib ko'ring.",
            'DATABASE_ERROR': "Ma'lumotlar bazasida xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'CACHE_ERROR': "Keshda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.",
            'AUTHENTICATION_ERROR': "Autentifikatsiya xatoligi. Iltimos, qayta urinib ko'ring.",
            'RATE_LIMIT_ERROR': "Juda ko'p so'rov yuborildi. Iltimos, biroz kuting va qayta urinib ko'ring.",
            'UNSUPPORTED_FORMAT': "Ushbu format qo'llab-quvvatlanmaydi.",
            'INVALID_URL': "Noto'g'ri havola. Iltimos, to'g'ri havola kiriting.",
            'SERVER_ERROR': "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
        }

        return error_messages.get(error_type, "Kutilmagan xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring.")

    def _format_admin_error_message(
        self,
        error_message: str,
        error_type: str,
        context: Optional[dict],
        user_chat_id: Optional[str]
    ) -> str:
        """Format error message for admin notification."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        message_parts = [
            f"🚨 <b>Bot Error Notification</b>",
            f"",
            f"⏰ <b>Time:</b> {timestamp}",
            f"🔴 <b>Error Type:</b> {error_type}",
            f"💬 <b>Error Message:</b> {error_message}",
        ]

        if user_chat_id:
            message_parts.append(f"👤 <b>User Chat ID:</b> {user_chat_id}")

        if context:
            message_parts.append(f"")
            message_parts.append(f"📋 <b>Context:</b>")
            for key, value in context.items():
                message_parts.append(f"  • {key}: {value}")

        return "\n".join(message_parts)

    def classify_error(self, error: Exception) -> str:
        """Classify error type based on exception."""
        error_str = str(error).lower()
        error_type = type(error).__name__

        # Network related errors
        if any(keyword in error_str for keyword in ['connection', 'timeout', 'network', 'unreachable']):
            return 'NETWORK_ERROR'
        
        # File related errors
        if 'file not found' in error_str or isinstance(error, FileNotFoundError):
            return 'FILE_NOT_FOUND'
        
        # Size related errors
        if any(keyword in error_str for keyword in ['size', 'large', 'limit', 'exceed']):
            return 'FILE_SIZE_ERROR'
        
        # Validation errors
        if any(keyword in error_str for keyword in ['validation', 'invalid', 'format']) or isinstance(error, ValueError):
            return 'VALIDATION_ERROR'
        
        # API specific errors
        if 'instagram' in error_str:
            return 'INSTAGRAM_API_ERROR'
        elif 'youtube' in error_str:
            return 'YOUTUBE_API_ERROR'
        elif 'music' in error_str or 'search' in error_str:
            return 'MUSIC_SEARCH_ERROR'
        elif 'telegram' in error_str:
            return 'TELEGRAM_ERROR'
        elif 'download' in error_str:
            return 'DOWNLOAD_ERROR'
        elif 'database' in error_str or 'sqlite' in error_str:
            return 'DATABASE_ERROR'
        elif 'cache' in error_str:
            return 'CACHE_ERROR'
        elif 'auth' in error_str or 'token' in error_str:
            return 'AUTHENTICATION_ERROR'
        elif 'rate' in error_str or 'limit' in error_str:
            return 'RATE_LIMIT_ERROR'
        elif 'url' in error_str or 'link' in error_str:
            return 'INVALID_URL'
        
        # Default to server error
        return 'SERVER_ERROR'

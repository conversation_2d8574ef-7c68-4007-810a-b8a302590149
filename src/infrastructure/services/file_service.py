"""File service implementation."""
import os
import uuid
import logging

from src.domain.interfaces.services import IFileService
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class FileService(IFileService):
    """File service implementation."""

    def __init__(self, base_directory: str = None):
        self.base_directory = base_directory or settings.file_storage.downloads_dir

    async def generate_unique_filename(self, extension: str) -> str:
        """Generate unique filename."""
        try:
            # Ensure extension starts with a dot
            if not extension.startswith('.'):
                extension = '.' + extension
                
            unique_id = str(uuid.uuid4())
            filename = f"{unique_id}{extension}"
            
            # Return full path
            full_path = os.path.join(self.base_directory, filename)
            
            logger.info(f"Generated unique filename: {full_path}")
            return full_path
            
        except Exception as e:
            logger.error(f"Error generating unique filename: {e}")
            raise

    async def cleanup_file(self, file_path: str) -> None:
        """Clean up temporary file."""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Cleaned up file: {file_path}")
            else:
                logger.warning(f"File not found for cleanup: {file_path}")
                
        except Exception as e:
            logger.error(f"Error cleaning up file {file_path}: {e}")
            # Don't raise exception for cleanup failures
            pass

    async def ensure_directory_exists(self, directory: str) -> None:
        """Ensure directory exists."""
        try:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Ensured directory exists: {directory}")
            
        except Exception as e:
            logger.error(f"Error ensuring directory exists {directory}: {e}")
            raise

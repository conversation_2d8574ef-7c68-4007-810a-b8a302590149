"""Use case for downloading media."""
import logging

from src.application.dtos.download_dtos import DownloadMediaRequest, DownloadMediaResponse
from src.domain.value_objects.url import Url
from src.domain.value_objects.identifiers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, UserId
from src.domain.interfaces.services import (
    IInstagramService,
    IYouTubeService,
    ITelegramService,
    IFileService
)
from src.domain.interfaces.repositories import IHistoryRepository, IMediaRepository
from src.domain.entities.media_item import MediaType

logger = logging.getLogger(__name__)


class DownloadMediaUseCase:
    """Use case for downloading media from various platforms."""

    def __init__(
        self,
        instagram_service: IInstagramService,
        youtube_service: IYouTubeService,
        telegram_service: ITelegramService,
        file_service: IFileService,
        history_repository: IHistoryRepository,
        media_repository: IMediaRepository
    ):
        self._instagram_service = instagram_service
        self._youtube_service = youtube_service
        self._telegram_service = telegram_service
        self._file_service = file_service
        self._history_repository = history_repository
        self._media_repository = media_repository

    async def execute(self, request: DownloadMediaRequest) -> DownloadMediaResponse:
        """Execute the download media use case."""
        try:
            # Validate and create value objects
            url = Url(request.url)
            chat_id = ChatId(request.chat_id)
            user_id = UserId(request.chat_id)

            # Route to appropriate service based on URL
            if url.is_instagram:
                return await self._handle_instagram_download(url, chat_id, user_id)
            elif url.is_youtube:
                # Auto-detect media type based on video_format if not explicitly set
                media_type = request.media_type
                if media_type == "auto" and request.video_format == "mp3":
                    media_type = "audio"

                # Handle YouTube based on media type
                if media_type == "audio":
                    return await self._handle_youtube_audio_download(url, chat_id, user_id, request)
                elif media_type == "video":
                    return await self._handle_youtube_download(url, chat_id, user_id, request)
                else:  # auto - default to video
                    return await self._handle_youtube_download(url, chat_id, user_id, request)
            else:
                await self._telegram_service.send_message(
                    chat_id, 
                    "Invalid URL. Please send a valid Instagram or YouTube URL."
                )
                return DownloadMediaResponse(
                    success=False,
                    message="Invalid URL format"
                )

        except ValueError as e:
            logger.error(f"Validation error: {e}")
            return DownloadMediaResponse(
                success=False,
                message=f"Validation error: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error in download use case: {e}")
            return DownloadMediaResponse(
                success=False,
                message="An unexpected error occurred"
            )

    async def _handle_instagram_download(
        self, 
        url: Url, 
        chat_id: ChatId, 
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle Instagram media download."""
        try:
            # Get media info
            media_info = await self._instagram_service.get_media_info(url)
            
            # Send caption if available
            if media_info.caption:
                await self._telegram_service.send_message(
                    chat_id, 
                    f"Caption: {media_info.caption}"
                )

            # Handle different media types
            if media_info.media_type == MediaType.ALBUM:
                return await self._handle_album_download(url, chat_id, user_id)
            else:
                return await self._handle_single_media_download(
                    media_info, url, chat_id, user_id
                )

        except Exception as e:
            logger.error(f"Error handling Instagram download: {e}")
            await self._telegram_service.send_message(
                chat_id,
                "Instagram'dan media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            )
            return DownloadMediaResponse(
                success=False,
                message="Instagram'dan media yuklab olishda xatolik yuz berdi."
            )

    async def _handle_youtube_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId,
        request: DownloadMediaRequest
    ) -> DownloadMediaResponse:
        """Handle YouTube video download."""
        try:
            # Get bot username for FastSaver API
            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            # Download video
            result = await self._youtube_service.download_video(url, bot_username, request.video_format)
            
            if not result.success:
                await self._telegram_service.send_message(
                    chat_id,
                    "YouTube'dan video yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                )
                return DownloadMediaResponse(
                    success=False,
                    message="YouTube'dan video yuklab olishda xatolik yuz berdi."
                )

            # Send video to Telegram
            if result.telegram_file_id:
                # Use file_id from FastSaver API
                file_id = await self._telegram_service.send_video_by_file_id(
                    chat_id=chat_id,
                    file_id=result.telegram_file_id,
                    caption=result.title  # Use video title as caption
                )
            else:
                # Use local file from yt-dlp
                file_id = await self._telegram_service.send_video(
                    chat_id=chat_id,
                    video_path=result.file_path,
                    caption=result.title,  # Use video title as caption
                    duration=result.duration,
                    width=result.width,
                    height=result.height,
                    thumbnail_path=result.thumbnail_path
                )

            # Save to history
            await self._history_repository.save_download_history(
                user_id, url, file_id
            )

            # Cleanup files (only for local downloads)
            if result.file_path and not result.telegram_file_id:
                await self._file_service.cleanup_file(result.file_path)
            if result.thumbnail_path and not result.telegram_file_id:
                await self._file_service.cleanup_file(result.thumbnail_path)

            return DownloadMediaResponse(
                success=True,
                message="YouTube video sent successfully",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error handling YouTube download: {e}")
            await self._telegram_service.send_message(
                chat_id, 
                f"Error downloading YouTube video: {str(e)}"
            )
            return DownloadMediaResponse(
                success=False,
                message=str(e)
            )

    async def _handle_youtube_audio_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId,
        request: DownloadMediaRequest
    ) -> DownloadMediaResponse:
        """Handle YouTube audio download."""
        try:
            # Get bot username for FastSaver API
            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            # Download audio
            result = await self._youtube_service.download_audio(url, bot_username, request.video_format)
            
            if not result.success:
                await self._telegram_service.send_message(
                    chat_id, 
                    f"Error downloading YouTube audio: {result.message}"
                )
                return DownloadMediaResponse(
                    success=False,
                    message=result.message
                )

            # Send audio to Telegram
            if result.telegram_file_id:
                # Use file_id from FastSaver API
                file_id = await self._telegram_service.send_audio_by_file_id(
                    chat_id=chat_id,
                    file_id=result.telegram_file_id,
                    caption=result.title,  # Use music name as caption
                    title=result.title  # Use music name as title
                )
            else:
                # Use local file from yt-dlp
                file_id = await self._telegram_service.send_audio(
                    chat_id=chat_id,
                    audio_path=result.file_path,
                    caption=result.title,  # Use music name as caption
                    title=result.title  # Use music name as title
                )

            # Save to history
            await self._history_repository.save_download_history(
                user_id, url, file_id
            )

            # Cleanup files (only for local downloads)
            if result.file_path and not result.telegram_file_id:
                await self._file_service.cleanup_file(result.file_path)

            return DownloadMediaResponse(
                success=True,
                message="YouTube audio sent successfully",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error handling YouTube audio download: {e}")
            await self._telegram_service.send_message(
                chat_id, 
                f"Error downloading YouTube audio: {str(e)}"
            )
            return DownloadMediaResponse(
                success=False,
                message=str(e)
            )

    async def _handle_single_media_download(
        self, 
        media_info, 
        url: Url, 
        chat_id: ChatId, 
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle single media item download."""
        try:
            # Download media content
            content = await self._instagram_service.download_media(media_info)
            
            # Generate unique filename
            extension = ".mp4" if media_info.media_type == MediaType.VIDEO else ".jpg"
            filename = await self._file_service.generate_unique_filename(extension)
            
            # Save file temporarily
            await self._media_repository.save_file(filename, content)
            
            # Send to Telegram
            if media_info.media_type == MediaType.VIDEO:
                file_id = await self._telegram_service.send_video(
                    chat_id=chat_id,
                    video_path=filename,
                    caption=media_info.caption,
                    duration=media_info.duration
                )
            else:
                file_id = await self._telegram_service.send_photo(
                    chat_id=chat_id,
                    photo_path=filename,
                    caption=media_info.caption
                )

            # Save to history
            await self._history_repository.save_download_history(
                user_id, url, file_id
            )

            # Cleanup
            await self._file_service.cleanup_file(filename)

            return DownloadMediaResponse(
                success=True,
                message="Media sent successfully",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error handling single media download: {e}")
            raise

    async def _handle_album_download(
        self, 
        url: Url, 
        chat_id: ChatId, 
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle album download."""
        try:
            album = await self._instagram_service.get_album_info(url)
            
            await self._telegram_service.send_message(
                chat_id, 
                f"📸 Album detected with {album.total_items} items. Downloading..."
            )

            file_ids = []
            for item in album.items:
                try:
                    # Download item content
                    content = await self._instagram_service.download_media(item)
                    
                    # Generate filename
                    extension = ".mp4" if item.media_type == MediaType.VIDEO else ".jpg"
                    filename = await self._file_service.generate_unique_filename(extension)
                    
                    # Save temporarily
                    await self._media_repository.save_file(filename, content)
                    
                    # Send to Telegram
                    caption = f"Album item {item.index}/{item.total_items}"
                    if item.media_type == MediaType.VIDEO:
                        file_id = await self._telegram_service.send_video(
                            chat_id=chat_id,
                            video_path=filename,
                            caption=caption
                        )
                    else:
                        file_id = await self._telegram_service.send_photo(
                            chat_id=chat_id,
                            photo_path=filename,
                            caption=caption
                        )
                    
                    file_ids.append(file_id)
                    
                    # Cleanup
                    await self._file_service.cleanup_file(filename)
                    
                except Exception as item_error:
                    logger.error(f"Error processing album item {item.index}: {item_error}")
                    await self._telegram_service.send_message(
                        chat_id, 
                        f"Error downloading album item {item.index}. Skipping..."
                    )
                    continue

            # Save to history (using first file_id)
            if file_ids:
                await self._history_repository.save_download_history(
                    user_id, url, file_ids[0]
                )

            return DownloadMediaResponse(
                success=True,
                message=f"Album with {len(file_ids)} items sent successfully",
                file_id=file_ids[0] if file_ids else None
            )

        except Exception as e:
            logger.error(f"Error handling album download: {e}")
            raise

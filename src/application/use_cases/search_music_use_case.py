"""Use case for searching music."""
import logging

from src.application.dtos.search_dtos import (
    SearchMusicRequest, 
    SearchMusicResponse, 
    MusicSearchResultDto
)
from src.domain.interfaces.services import IMusicSearchService

logger = logging.getLogger(__name__)


class SearchMusicUseCase:
    """Use case for searching music."""

    def __init__(self, music_search_service: IMusicSearchService):
        self._music_search_service = music_search_service

    async def execute(self, request: SearchMusicRequest) -> SearchMusicResponse:
        """Execute the search music use case."""
        try:
            logger.info(f"Searching for music with query: {request.query}, page: {request.page}")

            # Call the music search service
            domain_response = await self._music_search_service.search_music(
                query=request.query,
                page=request.page
            )

            # Convert domain entities to DTOs
            result_dtos = [
                MusicSearchResultDto(
                    title=result.title,
                    shortcode=result.shortcode,
                    duration=result.duration,
                    thumb=result.thumb,
                    thumb_best=result.thumb_best
                )
                for result in domain_response.results
            ]

            return SearchMusicResponse(
                error=domain_response.error,
                page=domain_response.page,
                results=result_dtos
            )

        except Exception as e:
            logger.error(f"Error in search music use case: {e}")
            return SearchMusicResponse(
                error=True,
                page=request.page,
                results=[]
            )

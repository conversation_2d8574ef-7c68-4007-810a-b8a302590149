"""DTOs for search operations."""
from dataclasses import dataclass
from typing import List, Optional


@dataclass
class SearchMusicRequest:
    """Request DTO for searching music."""
    query: str
    page: int = 1


@dataclass
class MusicSearchResultDto:
    """DTO for individual music search result."""
    title: str
    shortcode: str
    duration: str
    thumb: str
    thumb_best: str


@dataclass
class SearchMusicResponse:
    """Response DTO for music search."""
    error: bool
    page: int
    results: List[MusicSearchResultDto]


@dataclass
class DownloadFromShortcodeRequest:
    """Request DTO for downloading from shortcode."""
    chat_id: int
    shortcode: str
    bot_token: str
    media_type: str = "audio"


@dataclass
class DownloadFromShortcodeResponse:
    """Response DTO for downloading from shortcode."""
    success: bool
    message: str
    file_id: Optional[str] = None

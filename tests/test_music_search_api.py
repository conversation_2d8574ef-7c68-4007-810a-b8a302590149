"""Tests for music search API endpoints."""
import pytest
import sys
import os
from fastapi.testclient import TestClient

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app

client = TestClient(app)


class TestMusicSearchEndpoint:
    """Test music search endpoint."""

    def test_search_music_endpoint_exists(self):
        """Test that the search music endpoint exists."""
        response = client.get("/api/search-music?query=test")
        # Should not return 404 (endpoint exists)
        assert response.status_code != 404

    def test_search_music_with_valid_query(self):
        """Test music search with valid query."""
        response = client.get("/api/search-music?query=Billie&page=1")
        assert response.status_code == 200
        
        data = response.json()
        assert "error" in data
        assert "page" in data
        assert "results" in data
        assert isinstance(data["results"], list)

    def test_search_music_with_invalid_query(self):
        """Test music search with invalid query."""
        response = client.get("/api/search-music?query=")
        # Should return validation error
        assert response.status_code == 422

    def test_search_music_pagination(self):
        """Test music search pagination."""
        response = client.get("/api/search-music?query=test&page=2")
        assert response.status_code == 200
        
        data = response.json()
        assert data["page"] == 2


class TestDownloadFromShortcodeEndpoint:
    """Test download from shortcode endpoint."""

    def test_download_shortcode_endpoint_exists(self):
        """Test that the download shortcode endpoint exists."""
        response = client.post("/api/download-shortcode", json={
            "chat_id": 123,
            "shortcode": "test",
            "bot_token": "test_token"
        })
        # Should not return 404 (endpoint exists)
        assert response.status_code != 404

    def test_download_shortcode_validation(self):
        """Test download shortcode validation."""
        # Test missing required fields
        response = client.post("/api/download-shortcode", json={})
        assert response.status_code == 422

        # Test invalid chat_id
        response = client.post("/api/download-shortcode", json={
            "chat_id": "invalid",
            "shortcode": "test",
            "bot_token": "test_token"
        })
        assert response.status_code == 422

        # Test invalid shortcode
        response = client.post("/api/download-shortcode", json={
            "chat_id": 123,
            "shortcode": "",
            "bot_token": "test_token"
        })
        assert response.status_code == 422
